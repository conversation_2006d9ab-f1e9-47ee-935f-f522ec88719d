// This file is automatically generated by Keystone, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource postgresql {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "postgresql"
}

generator client {
  provider = "prisma-client-js"
}

model Todo {
  id           String  @id @default(cuid())
  label        String  @default("")
  isComplete   Boolean @default(false)
  isPrivate    <PERSON><PERSON><PERSON> @default(false)
  assignedTo   User?   @relation("Todo_assignedTo", fields: [assignedToId], references: [id])
  assignedToId String? @map("assignedTo")

  @@index([assignedToId])
}

model User {
  id       String  @id @default(cuid())
  name     String  @default("")
  email    String  @unique @default("")
  password String
  role     Role?   @relation("User_role", fields: [roleId], references: [id])
  roleId   String? @map("role")
  tasks    Todo[]  @relation("Todo_assignedTo")

  @@index([roleId])
}

model Role {
  id                 String  @id @default(cuid())
  name               String  @default("")
  canCreateTodos     <PERSON>an @default(false)
  canManageAllTodos  Boolean @default(false)
  canSeeOtherPeople  Boolean @default(false)
  canEditOtherPeople Boolean @default(false)
  canManagePeople    Boolean @default(false)
  canManageRoles     Boolean @default(false)
  canAccessDashboard Boolean @default(false)
  assignedTo         User[]  @relation("User_role")
}
