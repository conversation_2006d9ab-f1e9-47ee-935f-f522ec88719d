/**
 * Auto-generated field type mapping from views index
 * This file is generated by index-to-view.ts during migration generation
 * DO NOT EDIT THIS FILE MANUALLY
 */

/**
 * Get the field type from a field's viewsIndex
 * @param viewsIndex The views index of the field
 * @returns The field type name
 */
export function getFieldTypeFromViewsIndex(viewsIndex: number): string {
  const viewsIndexToType: Record<number, string> = {
    0: "id",
    1: "text",
    2: "checkbox",
    3: "relationship",
    4: "password"
  };

  const fieldType = viewsIndexToType[viewsIndex];
  if (!fieldType) {
    throw new Error(`Invalid views index: ${viewsIndex}`);
  }

  return fieldType;
}